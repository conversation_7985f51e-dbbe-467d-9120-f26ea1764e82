<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
 "http://www.w3.org/TR/html4/loose.dtd">
<html><head>
<title>3PCAP man page</title>
<meta name="generator" content="roffit">
<STYLE type="text/css">
pre {
  overflow: auto;
  margin: 0;
}

P.level0, pre.level0 {
 padding-left: 2em;
}

P.level1, pre.level1 {
 padding-left: 4em;
}

P.level2, pre.level2 {
 padding-left: 6em;
}

span.emphasis {
 font-style: italic;
}

span.bold {
 font-weight: bold;
}

span.manpage {
 font-weight: bold;
}

h2.nroffsh {
 background-color: #e0e0e0;
}

span.nroffip {
 font-weight: bold;
 font-size: 120%;
 font-family: monospace;
}

p.roffit {
 text-align: center;
 font-size: 80%;
}
</STYLE>
</head><body>
<a name="NAME"></a><h2 class="nroffsh">Name</h2>
<p class="level0">pcap_setnonblock, pcap_getnonblock - set or get the state of non-blocking mode on a capture device </p><a name="SYNOPSIS"></a><h2 class="nroffsh">Synopsis</h2><pre class="level0">
&#35;include &lt;pcap/pcap.h&gt;
char errbuf[PCAP_ERRBUF_SIZE];
int pcap_setnonblock(pcap_t *p, int nonblock, char *errbuf);
int pcap_getnonblock(pcap_t *p, char *errbuf);
</pre>
<a name="DESCRIPTION"></a><h2 class="nroffsh">Description</h2>
<p class="level0"><span Class="bold">pcap_setnonblock</span>() puts a capture handle into ``non-blocking&#39;&#39; mode, or takes it out of ``non-blocking&#39;&#39; mode, depending on whether the <span Class="emphasis">nonblock</span> argument is non-zero or zero.  It has no effect on ``savefiles&#39;&#39;. <span Class="emphasis">errbuf</span> is a buffer large enough to hold at least <span Class="bold">PCAP_ERRBUF_SIZE</span> chars. </p>
<p class="level0">In ``non-blocking&#39;&#39; mode, an attempt to read from the capture descriptor with <span Class="bold">pcap_dispatch</span>(3PCAP) and <a Class="bold" href="./pcap_next_ex.html">pcap_next_ex</a>(3PCAP) will, if no packets are currently available to be read, return <span Class="bold">0</span> immediately rather than blocking waiting for packets to arrive. </p>
<p class="level0"><a Class="bold" href="./pcap_loop.html">pcap_loop</a>(3PCAP) will loop forever, consuming CPU time when no packets are currently available; <span Class="bold">pcap_dispatch</span>() should be used instead. <span Class="bold">pcap_next</span>(3PCAP) will return <span Class="bold">NULL</span> if there are no packets currently available to read; this is indistinguishable from an error, so <span Class="bold">pcap_next_ex</span>() should be used instead. </p>
<p class="level0">When first activated with <a Class="bold" href="./pcap_activate.html">pcap_activate</a>(3PCAP) or opened with <a Class="bold" href="./pcap_open_live.html">pcap_open_live</a>(3PCAP), a capture handle is not in ``non-blocking mode&#39;&#39;; a call to <span Class="bold">pcap_setnonblock</span>() is required in order to put it into ``non-blocking&#39;&#39; mode. </p><a name="RETURN"></a><h2 class="nroffsh">Return value</h2>
<p class="level0"><span Class="bold">pcap_setnonblock()</span> return 0 on success, <span Class="bold">PCAP_ERROR_NOT_ACTIVATED</span> if called on a capture handle that has been created but not activated, and <span Class="bold">PCAP_ERROR</span> for other errors. <span Class="bold">pcap_getnonblock</span>() returns the current ``non-blocking&#39;&#39; state of the capture descriptor on success; it always returns <span Class="bold">0</span> on ``savefiles&#39;&#39;. It returns <span Class="bold">PCAP_ERROR_NOT_ACTIVATED</span> if called on a capture handle that has been created but not activated, and <span Class="bold">PCAP_ERROR</span> for other errors. If <span Class="bold">PCAP_ERROR</span> is returned, <span Class="emphasis">errbuf</span> is filled in with an appropriate error message. </p><a name="SEE"></a><h2 class="nroffsh">See also</h2>
<p class="level0"><a Class="bold" href="./pcap.html">pcap</a>(3PCAP), <a Class="bold" href="./pcap_next_ex.html">pcap_next_ex</a>(3PCAP), <a Class="bold" href="./pcap_geterr.html">pcap_geterr</a>(3PCAP) </p><p class="roffit">
 This HTML page was made with <a href="https://daniel.haxx.se/projects/roffit/">roffit</a>.
</body></html>
