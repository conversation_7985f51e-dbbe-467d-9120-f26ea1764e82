<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
 "http://www.w3.org/TR/html4/loose.dtd">
<html><head>
<title>3PCAP man page</title>
<meta name="generator" content="roffit">
<STYLE type="text/css">
pre {
  overflow: auto;
  margin: 0;
}

P.level0, pre.level0 {
 padding-left: 2em;
}

P.level1, pre.level1 {
 padding-left: 4em;
}

P.level2, pre.level2 {
 padding-left: 6em;
}

span.emphasis {
 font-style: italic;
}

span.bold {
 font-weight: bold;
}

span.manpage {
 font-weight: bold;
}

h2.nroffsh {
 background-color: #e0e0e0;
}

span.nroffip {
 font-weight: bold;
 font-size: 120%;
 font-family: monospace;
}

p.roffit {
 text-align: center;
 font-size: 80%;
}
</STYLE>
</head><body>
<a name="NAME"></a><h2 class="nroffsh">Name</h2>
<p class="level0">pcap_get_selectable_fd - get a file descriptor on which a select() can be done for a live capture </p><a name="SYNOPSIS"></a><h2 class="nroffsh">Synopsis</h2><pre class="level0">
&#35;include &lt;pcap/pcap.h&gt;
int pcap_get_selectable_fd(pcap_t *p);
</pre>
<a name="DESCRIPTION"></a><h2 class="nroffsh">Description</h2>
<p class="level0"><span Class="bold">pcap_get_selectable_fd</span>() returns, on UNIX, a file descriptor number for a file descriptor on which one can do a <span Class="bold">select</span>(2), <span Class="bold">poll</span>(2), <span Class="bold">epoll_wait</span>(2), <span Class="bold">kevent</span>(2), or other such call to wait for it to be possible to read packets without blocking, if such a descriptor exists, or <span Class="bold">-1</span>, if no such descriptor exists. </p>
<p class="level0">Some network devices opened with <a Class="bold" href="./pcap_create.html">pcap_create</a>(3PCAP) and <a Class="bold" href="./pcap_activate.html">pcap_activate</a>(3PCAP), or with <a Class="bold" href="./pcap_open_live.html">pcap_open_live</a>(3PCAP), do not support those calls (for example, regular network devices on FreeBSD 4.3 and 4.4, and Endace DAG devices), so <span Class="bold">-1</span> is returned for those devices.  In that case, those calls must be given a timeout less than or equal to the timeout returned by <a Class="bold" href="./pcap_get_required_select_timeout.html">pcap_get_required_select_timeout</a>(3PCAP) for the device for which <span Class="bold">pcap_get_selectable_fd</span>() returned <span Class="bold">-1</span>, the device must be put in non-blocking mode with a call to <span Class="bold">\%pcap_setnonblock</span>(3PCAP), and an attempt must always be made to read packets from the device when the call returns.  If <span Class="bold">\%pcap_get_required_select_timeout</span>() returns <span Class="bold">NULL</span>, it is not possible to wait for packets to arrive on the device in an event loop. </p>
<p class="level0">Note that a device on which a read can be done without blocking may, on some platforms, not have any packets to read if the packet buffer timeout has expired.  A call to <span Class="bold">pcap_dispatch</span>(3PCAP) or <a Class="bold" href="./pcap_next_ex.html">pcap_next_ex</a>(3PCAP) will return 0 in this case, but will not block. </p>
<p class="level0">Note that in: </p>
<p class="level0"><a name=""></a><span class="nroffip"></span> </p>
<p class="level1">FreeBSD prior to FreeBSD 4.6; </p>
<p class="level0"><a name=""></a><span class="nroffip"></span> </p>
<p class="level1">NetBSD prior to NetBSD 3.0; </p>
<p class="level0"><a name=""></a><span class="nroffip"></span> </p>
<p class="level1">OpenBSD prior to OpenBSD 2.4; </p>
<p class="level0"><a name=""></a><span class="nroffip"></span> </p>
<p class="level1">Mac OS X prior to Mac OS X 10.7; </p>
<p class="level0"><span Class="bold">select</span>(), <span Class="bold">poll</span>(), and <span Class="bold">kevent</span>() do not work correctly on BPF devices; <span Class="bold">pcap_get_selectable_fd</span>() will return a file descriptor on most of those versions (the exceptions being FreeBSD 4.3 and 4.4), but a simple <span Class="bold">select</span>(), <span Class="bold">poll</span>(), or <span Class="bold">kevent</span>() call will not indicate that the descriptor is readable until a full buffer&#39;s worth of packets is received, even if the packet timeout expires before then.  To work around this, code that uses those calls to wait for packets to arrive must put the <span Class="bold">pcap_t</span> in non-blocking mode, and must arrange that the call have a timeout less than or equal to the packet buffer timeout, and must try to read packets after that timeout expires, regardless of whether the call indicated that the file descriptor for the <span Class="bold">pcap_t</span> is ready to be read or not.  (That workaround will not work in FreeBSD 4.3 and later; however, in FreeBSD 4.6 and later, those calls work correctly on BPF devices, so the workaround isn&#39;t necessary, although it does no harm.) </p>
<p class="level0">Note also that <span Class="bold">poll</span>() and <span Class="bold">kevent</span>() doesn&#39;t work on character special files, including BPF devices, in Mac OS X 10.4 and 10.5, so, while <span Class="bold">select</span>() can be used on the descriptor returned by <span Class="bold">pcap_get_selectable_fd</span>(), <span Class="bold">poll</span>() and <span Class="bold">kevent</span>() cannot be used on it those versions of Mac OS X. <span Class="bold">poll</span>(), but not <span Class="bold">kevent</span>(), works on that descriptor in Mac OS X releases prior to 10.4; <span Class="bold">poll</span>() and <span Class="bold">kevent</span>() work on that descriptor in Mac OS X 10.6 and later. </p>
<p class="level0"><span Class="bold">pcap_get_selectable_fd</span>() is not available on Windows. </p><a name="RETURN"></a><h2 class="nroffsh">Return value</h2>
<p class="level0">A selectable file descriptor is returned if one exists; otherwise, <span Class="bold">-1</span> is returned. </p><a name="SEE"></a><h2 class="nroffsh">See also</h2>
<p class="level0"><a Class="bold" href="./pcap.html">pcap</a>(3PCAP), <span Class="bold">kqueue</span>(2) </p><p class="roffit">
 This HTML page was made with <a href="https://daniel.haxx.se/projects/roffit/">roffit</a>.
</body></html>
