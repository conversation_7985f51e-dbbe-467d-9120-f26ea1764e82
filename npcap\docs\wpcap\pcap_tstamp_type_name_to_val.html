<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
 "http://www.w3.org/TR/html4/loose.dtd">
<html><head>
<title>3PCAP man page</title>
<meta name="generator" content="roffit">
<STYLE type="text/css">
pre {
  overflow: auto;
  margin: 0;
}

P.level0, pre.level0 {
 padding-left: 2em;
}

P.level1, pre.level1 {
 padding-left: 4em;
}

P.level2, pre.level2 {
 padding-left: 6em;
}

span.emphasis {
 font-style: italic;
}

span.bold {
 font-weight: bold;
}

span.manpage {
 font-weight: bold;
}

h2.nroffsh {
 background-color: #e0e0e0;
}

span.nroffip {
 font-weight: bold;
 font-size: 120%;
 font-family: monospace;
}

p.roffit {
 text-align: center;
 font-size: 80%;
}
</STYLE>
</head><body>
<a name="NAME"></a><h2 class="nroffsh">Name</h2>
<p class="level0">pcap_tstamp_type_name_to_val - get the time stamp type value corresponding to a time stamp type name </p><a name="SYNOPSIS"></a><h2 class="nroffsh">Synopsis</h2><pre class="level0">
&#35;include &lt;pcap/pcap.h&gt;
int pcap_tstamp_type_name_to_val(const char *name);
</pre>
<a name="DESCRIPTION"></a><h2 class="nroffsh">Description</h2>
<p class="level0"><span Class="bold">pcap_tstamp_type_name_to_val</span>() translates a time stamp type name to the corresponding time stamp type value.  The translation is case-insensitive. </p><a name="RETURN"></a><h2 class="nroffsh">Return value</h2>
<p class="level0"><span Class="bold">pcap_tstamp_type_name_to_val</span>() returns time stamp type value on success and <span Class="bold">PCAP_ERROR</span> on failure. </p><a name="BACKWARD"></a><h2 class="nroffsh">Backward compatibility</h2>
<p class="level0">This function became available in libpcap release 1.2.1. </p><a name="SEE"></a><h2 class="nroffsh">See also</h2>
<p class="level0"><a Class="bold" href="./pcap.html">pcap</a>(3PCAP), <a Class="bold" href="./pcap_tstamp_type_val_to_name.html">pcap_tstamp_type_val_to_name</a>(3PCAP) </p><p class="roffit">
 This HTML page was made with <a href="https://daniel.haxx.se/projects/roffit/">roffit</a>.
</body></html>
