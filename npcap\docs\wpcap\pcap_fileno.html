<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
 "http://www.w3.org/TR/html4/loose.dtd">
<html><head>
<title>3PCAP man page</title>
<meta name="generator" content="roffit">
<STYLE type="text/css">
pre {
  overflow: auto;
  margin: 0;
}

P.level0, pre.level0 {
 padding-left: 2em;
}

P.level1, pre.level1 {
 padding-left: 4em;
}

P.level2, pre.level2 {
 padding-left: 6em;
}

span.emphasis {
 font-style: italic;
}

span.bold {
 font-weight: bold;
}

span.manpage {
 font-weight: bold;
}

h2.nroffsh {
 background-color: #e0e0e0;
}

span.nroffip {
 font-weight: bold;
 font-size: 120%;
 font-family: monospace;
}

p.roffit {
 text-align: center;
 font-size: 80%;
}
</STYLE>
</head><body>
<a name="NAME"></a><h2 class="nroffsh">Name</h2>
<p class="level0">pcap_fileno - get the file descriptor for a live capture </p><a name="SYNOPSIS"></a><h2 class="nroffsh">Synopsis</h2><pre class="level0">
&#35;include &lt;pcap/pcap.h&gt;
int pcap_fileno(pcap_t *p);
</pre>
<a name="DESCRIPTION"></a><h2 class="nroffsh">Description</h2>
<p class="level0">If <span Class="emphasis">p</span> refers to a network device that was opened for a live capture using a combination of <a Class="bold" href="./pcap_create.html">pcap_create</a>(3PCAP) and <a Class="bold" href="./pcap_activate.html">pcap_activate</a>(3PCAP), or using <a Class="bold" href="./pcap_open_live.html">pcap_open_live</a>(3PCAP), <span Class="bold">pcap_fileno</span>() returns the file descriptor from which captured packets are read. </p>
<p class="level0">If <span Class="emphasis">p</span> refers to a ``savefile&#39;&#39; that was opened using functions such as <a Class="bold" href="./pcap_open_offline.html">pcap_open_offline</a>(3PCAP) or <span Class="bold">pcap_fopen_offline</span>(3PCAP), a ``dead&#39;&#39; <span Class="bold">pcap_t</span> opened using <a Class="bold" href="./pcap_open_dead.html">pcap_open_dead</a>(3PCAP), or a <span Class="bold">pcap_t</span> that was created with <span Class="bold">pcap_create</span>() but that has not yet been activated with <span Class="bold">pcap_activate</span>(), it returns <span Class="bold">PCAP_ERROR</span>. </p><a name="SEE"></a><h2 class="nroffsh">See also</h2>
<p class="level0"><a Class="bold" href="./pcap.html">pcap</a>(3PCAP) </p><p class="roffit">
 This HTML page was made with <a href="https://daniel.haxx.se/projects/roffit/">roffit</a>.
</body></html>
