#!/usr/bin/env python3
"""
Test script for pure reverse DNS detection (no IP range mapping).
"""

import socket
import subprocess
import requests
import time

def get_ip_for_domain(domain):
    """Get IP address for a domain"""
    try:
        ip = socket.gethostbyname(domain)
        return ip
    except socket.gaierror:
        return None

def reverse_dns_lookup(ip):
    """Perform reverse DNS lookup using nslookup"""
    try:
        # Create reverse IP format
        octets = ip.split('.')
        reverse_ip = f"{octets[3]}.{octets[2]}.{octets[1]}.{octets[0]}.in-addr.arpa"
        
        print(f"  Querying: {reverse_ip}")
        
        # Run nslookup
        result = subprocess.run(['nslookup', reverse_ip], 
                              capture_output=True, text=True, timeout=10)
        
        print(f"  nslookup output: {result.stdout.strip()}")
        
        # Parse output
        for line in result.stdout.split('\n'):
            if 'name =' in line:
                hostname = line.split('name =')[1].strip().rstrip('.')
                return hostname
        
        return None
    except Exception as e:
        print(f"  Error in reverse DNS lookup: {e}")
        return None

def test_reverse_dns_only():
    """Test pure reverse DNS lookups"""
    test_domains = [
        "youtube.com",
        "www.youtube.com", 
        "google.com",
        "facebook.com",
        "twitter.com",
        "example.com"
    ]
    
    print("Pure Reverse DNS Test")
    print("====================")
    print("Testing reverse DNS without IP range mapping")
    
    for domain in test_domains:
        print(f"\nTesting {domain}:")
        
        # Get IP
        ip = get_ip_for_domain(domain)
        if not ip:
            print(f"  Could not resolve {domain}")
            continue
            
        print(f"  Forward DNS: {domain} -> {ip}")
        
        # Try reverse DNS
        reverse_hostname = reverse_dns_lookup(ip)
        if reverse_hostname:
            print(f"  Reverse DNS: {ip} -> {reverse_hostname}")
            
            # Check if it would be blocked
            if any(blocked in reverse_hostname.lower() for blocked in ['youtube', 'facebook', 'twitter']):
                print(f"  Would be BLOCKED: {reverse_hostname}")
            else:
                print(f"  Would be ALLOWED: {reverse_hostname}")
        else:
            print(f"  Reverse DNS: {ip} -> No result")

def test_with_requests():
    """Make actual requests to trigger the Rust application"""
    print("\n" + "="*60)
    print("Making test requests...")
    print("Watch the Rust application for reverse DNS detections!")
    print("Should see 'Reverse DNS detection:' messages")
    
    test_urls = [
        "https://www.youtube.com",
        "https://youtube.com", 
        "https://www.google.com",
        "https://httpbin.org/get"
    ]
    
    for url in test_urls:
        try:
            print(f"\nMaking request to: {url}")
            response = requests.get(url, timeout=10)
            print(f"  Status: {response.status_code}")
        except Exception as e:
            print(f"  Error: {e}")
        
        time.sleep(4)  # Wait between requests to see individual detections

if __name__ == "__main__":
    print("Pure Reverse DNS Test Script")
    print("This tests reverse DNS lookups without IP range mapping.")
    print("Make sure the Rust application is running with admin privileges!")
    
    input("Press Enter to start reverse DNS tests...")
    test_reverse_dns_only()
    
    input("\nPress Enter to make test requests...")
    test_with_requests()
    
    print("\nTest completed. Check the Rust application console for 'Reverse DNS detection:' messages.")
