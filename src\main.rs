use windivert::prelude::*;
use dns_parser::Packet as DnsPacket;

fn main() {
    let filter = "udp.DstPort == 53 and outbound"; // Catch outbound DNS requests
    let flags = WinDivertFlags::new().set_recv_only();

    let handle = WinDivert::network(filter, 0, flags)
        .expect("Failed to open WinDivert. Are you running as Admin?");

    println!("Capturing outbound DNS queries...");

    let mut buffer = [0u8; 65535];

    loop {
        match handle.recv(Some(&mut buffer)) {
            Ok(packet) => {
                /*if let Ok(packet) = DnsPacket::parse(&buffer[..packet_len]) {
                    if let Some(payload) = packet.payload {
                        if let Ok(dns) = DnsPacket::parse(payload) {
                            for question in dns.questions {
                                println!("🔍 DNS Query: {}", question.qname);
                                // TODO: send to accountability server or log
                            }
                        }
                    }
                }*/
                if let Ok(dns) = DnsPacket::parse(&packet.data) {
                    for question in dns.questions {
                        println!("🔍 DNS Query: {}", question.qname);
                        // TODO: send to accountability server or log
                    }
                }
            }
            Err(e) => eprintln!("Error capturing packet: {}", e),
        }
    }
}