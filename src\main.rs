use tray_item::{Icon<PERSON>ource, TrayItem};
use pcap::Device;
use pnet::packet::ethernet::{EtherTypes, EthernetPacket};
use pnet::packet::ip::IpNextHeaderProtocols;
use pnet::packet::ipv4::Ipv4Packet;
use pnet::packet::tcp::TcpPacket;
use pnet::packet::Packet;
use notify_rust::Notification;
use std::collections::HashSet;
use std::fs;
use std::thread;
use std::time::Duration;

// Load blocked hosts from a hosts file
fn load_blocked_hosts(hosts_file_path: &str) -> HashSet<String> {
    let mut blocked_hosts = HashSet::new();

    if let Ok(content) = fs::read_to_string(hosts_file_path) {
        for line in content.lines() {
            let line = line.trim();
            // Skip comments and empty lines
            if line.is_empty() || line.starts_with('#') {
                continue;
            }

            // Parse hosts file format: "IP hostname"
            let parts: Vec<&str> = line.split_whitespace().collect();
            if parts.len() >= 2 {
                // Add the hostname (second part) to blocked list
                blocked_hosts.insert(parts[1].to_lowercase());
            }
        }
    } else {
        println!("Warning: Could not read hosts file at {}", hosts_file_path);
    }

    blocked_hosts
}

// Extract HTTP Host header from TCP payload
fn extract_http_host(payload: &[u8]) -> Option<String> {
    if payload.is_empty() {
        return None;
    }

    let payload_str = String::from_utf8_lossy(payload);

    // Check if this looks like an HTTP request
    if !payload_str.starts_with("GET ") &&
       !payload_str.starts_with("POST ") &&
       !payload_str.starts_with("PUT ") &&
       !payload_str.starts_with("DELETE ") &&
       !payload_str.starts_with("HEAD ") &&
       !payload_str.starts_with("OPTIONS ") {
        return None;
    }

    // Look for Host header
    for line in payload_str.lines() {
        if line.to_lowercase().starts_with("host:") {
            let host = line[5..].trim(); // Remove "host:" prefix
            return Some(host.to_lowercase());
        }
    }

    None
}

// Extract hostname from TLS SNI (Server Name Indication)
fn extract_sni_hostname(payload: &[u8]) -> Option<String> {
    // Basic TLS handshake parsing to extract SNI
    // This is a simplified implementation
    if payload.len() < 43 {
        return None;
    }

    // Check if this is a TLS handshake (0x16) and client hello (0x01)
    if payload[0] != 0x16 || payload[5] != 0x01 {
        return None;
    }

    // Skip to extensions (this is a simplified approach)
    let mut offset = 43; // Skip fixed part of client hello

    // Skip session ID
    if offset >= payload.len() {
        return None;
    }
    let session_id_len = payload[offset] as usize;
    offset += 1 + session_id_len;

    // Skip cipher suites
    if offset + 2 >= payload.len() {
        return None;
    }
    let cipher_suites_len = u16::from_be_bytes([payload[offset], payload[offset + 1]]) as usize;
    offset += 2 + cipher_suites_len;

    // Skip compression methods
    if offset >= payload.len() {
        return None;
    }
    let compression_len = payload[offset] as usize;
    offset += 1 + compression_len;

    // Parse extensions
    if offset + 2 >= payload.len() {
        return None;
    }
    let extensions_len = u16::from_be_bytes([payload[offset], payload[offset + 1]]) as usize;
    offset += 2;

    let extensions_end = offset + extensions_len;
    while offset + 4 < extensions_end && offset + 4 < payload.len() {
        let ext_type = u16::from_be_bytes([payload[offset], payload[offset + 1]]);
        let ext_len = u16::from_be_bytes([payload[offset + 2], payload[offset + 3]]) as usize;
        offset += 4;

        // SNI extension type is 0x0000
        if ext_type == 0x0000 && offset + ext_len <= payload.len() {
            // Parse SNI extension
            if ext_len >= 5 {
                let _name_list_len = u16::from_be_bytes([payload[offset], payload[offset + 1]]) as usize;
                let name_type = payload[offset + 2]; // Should be 0x00 for hostname
                let name_len = u16::from_be_bytes([payload[offset + 3], payload[offset + 4]]) as usize;

                if name_type == 0x00 && offset + 5 + name_len <= payload.len() {
                    let hostname = String::from_utf8_lossy(&payload[offset + 5..offset + 5 + name_len]);
                    return Some(hostname.to_lowercase());
                }
            }
        }

        offset += ext_len;
    }

    None
}

// Send desktop notification with proper error handling
fn send_notification(host: &str) {
    // Try the primary notification method
    match Notification::new()
        .summary("Blocked Website Access")
        .body(&format!("Access attempt to blocked website: {}", host))
        .timeout(5000) // 5 seconds
        .show() {
        Ok(_) => {
            println!("✓ Notification sent successfully for: {}", host);
        },
        Err(e) => {
            println!("✗ Failed to send notification for {}: {}", host, e);

            // Fallback: Try a simpler notification without icon
            match Notification::new()
                .summary("Blocked Website")
                .body(&format!("Blocked: {}", host))
                .show() {
                Ok(_) => {
                    println!("✓ Fallback notification sent for: {}", host);
                },
                Err(e2) => {
                    println!("✗ Fallback notification also failed for {}: {}", host, e2);
                    // Even if notifications fail, continue execution
                }
            }
        }
    }
}

fn main() {
    // Set up panic handler to catch any unexpected panics
    std::panic::set_hook(Box::new(|panic_info| {
        println!("PANIC occurred: {}", panic_info);
        println!("The application will continue running...");
    }));

    // Load blocked hosts from hosts file
    let hosts_file_path = "blocked_hosts.txt"; // You can change this path
    let blocked_hosts = load_blocked_hosts(hosts_file_path);
    println!("Loaded {} blocked hosts from {}", blocked_hosts.len(), hosts_file_path);

    let icon : IconSource = IconSource::Resource("name-of-icon-in-rc-file");
    let mut tray = TrayItem::new("Paws To Yourself", icon).unwrap();

    tray.add_label("Running...").unwrap();
    tray.add_menu_item("Quit", || {
        std::process::exit(0);
    }).unwrap();

    for dev in Device::list().unwrap() {
        println!("Device: {} {}", dev.name, dev.desc.unwrap_or_default());
    }

    let device = pcap::Device::lookup()
        .expect("device lookup failed")
        .expect("no device available");
    println!("Using device {}", device.name);

    // Setup Capture
    let mut cap = pcap::Capture::from_device("\\Device\\NPF_{2C8501C8-0A69-4EA1-8114-BAC2626F5442}")
        .unwrap()
        .immediate_mode(true)
        .promisc(true)
        .open()
        .unwrap();

    println!("Starting capture loop");

    let mut packet_count = 0;
    let mut last_heartbeat = std::time::Instant::now();

    loop {
        match cap.next_packet() {
            Ok(packet) => {
                // Process the packet
                process_packet(&packet, &blocked_hosts);
                packet_count += 1;

                // Print heartbeat every 1000 packets or every 30 seconds
                let now = std::time::Instant::now();
                if packet_count % 1000 == 0 || now.duration_since(last_heartbeat).as_secs() >= 30 {
                    println!("Heartbeat: Processed {} packets, still running...", packet_count);
                    last_heartbeat = now;
                }
            },
            Err(e) => {
                println!("Error capturing packet: {}", e);
                // Continue the loop instead of exiting
                thread::sleep(Duration::from_millis(100));
                continue;
            }
        }
    }
}

fn process_packet(packet: &pcap::Packet, blocked_hosts: &HashSet<String>) {
    // Add safety check for packet data
    if packet.data.is_empty() {
        return;
    }

    if let Some(ethernet_packet) = EthernetPacket::new(&packet.data) {
            match ethernet_packet.get_ethertype() {
                EtherTypes::Ipv4 => {
                    if let Some(ipv4_packet) = Ipv4Packet::new(ethernet_packet.payload()) {
                        // Only process TCP packets (HTTP/HTTPS use TCP)
                        if ipv4_packet.get_next_level_protocol() == IpNextHeaderProtocols::Tcp {
                            if let Some(tcp_packet) = TcpPacket::new(ipv4_packet.payload()) {
                                // Check for HTTP traffic (port 80) or HTTPS (port 443)
                                let dest_port = tcp_packet.get_destination();
                                let _src_port = tcp_packet.get_source();

                                // Look for outgoing HTTP/HTTPS requests
                                if dest_port == 80 || dest_port == 443 {
                                    let payload = tcp_packet.payload();

                                    // For HTTP (port 80), we can parse the Host header directly
                                    if dest_port == 80 {
                                        if let Some(host) = extract_http_host(payload) {
                                            println!("HTTP request to: {}", host);

                                            // Check if host is in blocked list
                                            if blocked_hosts.contains(&host) {
                                                println!("BLOCKED: Access attempt to {}", host);
                                                println!("Sending notification...");
                                                send_notification(&host);
                                                println!("Notification sent, continuing...");
                                            }
                                        }
                                    }

                                    // For HTTPS (port 443), parse SNI from TLS handshake
                                    if dest_port == 443 && payload.len() > 0 {
                                        if let Some(hostname) = extract_sni_hostname(payload) {
                                            println!("HTTPS request to: {}", hostname);

                                            // Check if host is in blocked list
                                            if blocked_hosts.contains(&hostname) {
                                                println!("BLOCKED: Access attempt to {}", hostname);
                                                println!("Sending notification...");
                                                send_notification(&hostname);
                                                println!("Notification sent, continuing...");
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                },
                _ => {}
            }
        }
}
