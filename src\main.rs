use pnet::datalink::EtherType;
use tray_item::{IconSource, TrayItem};
use pcap::Device;
use pnet::packet::ethernet::{EtherTypes, EthernetPacket};
use pnet::packet::ip::IpNextHeaderProtocols;
use pnet::packet::tcp::TcpPacket;
use pnet::packet::udp::UdpPacket;
use pnet::packet::Packet;

fn main() {
    let icon : IconSource = IconSource::Resource("name-of-icon-in-rc-file");
    let mut tray = TrayItem::new("Paws To Yourself", icon).unwrap();

    tray.add_label("Running...").unwrap();
    tray.add_menu_item("Quit", || {
        std::process::exit(0);
    }).unwrap();

    for dev in Device::list().unwrap() {
        println!("Device: {} {}", dev.name, dev.desc.unwrap_or_default());
    }

    let device = pcap::Device::lookup()
        .expect("device lookup failed")
        .expect("no device available");
    println!("Using device {}", device.name);

    // Setup Capture
    let mut cap = pcap::Capture::from_device("\\Device\\NPF_{2C8501C8-0A69-4EA1-8114-BAC2626F5442}")
        .unwrap()
        .immediate_mode(true)
        .promisc(true)
        .open()
        .unwrap();

    println!("Capture opened");

    // get a packet and print its bytes
    println!("{:?}", cap.next_packet());

    println!("Starting capture loop");

    while let Ok(packet) = cap.next_packet() {
        //println!("received packet! {:?}", packet);

        if let Some(ethernet_packet) = EthernetPacket::new(&packet.data) {

            match ethernet_packet.get_ethertype() {
                EtherTypes::Ipv4 => {
                    // Handle TCP packets
                    let tcp_packet = TcpPacket::new(ethernet_packet.payload());
                    if let Some(tcp_packet) = tcp_packet {
                        println!(
                            "TCP Packet: {}:{} > {}:{}; Seq: {}, Ack: {}",
                            ethernet_packet.get_source(),
                            tcp_packet.get_source(),
                            ethernet_packet.get_destination(),
                            tcp_packet.get_destination(),
                            tcp_packet.get_sequence(),
                            tcp_packet.get_acknowledgement()
                        );
                        println!("Payload: {:?}", tcp_packet.payload());
                    }
                },
                _ => {}
            }
        }
    }
}
