<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
 "http://www.w3.org/TR/html4/loose.dtd">
<html><head>
<title>3PCAP man page</title>
<meta name="generator" content="roffit">
<STYLE type="text/css">
pre {
  overflow: auto;
  margin: 0;
}

P.level0, pre.level0 {
 padding-left: 2em;
}

P.level1, pre.level1 {
 padding-left: 4em;
}

P.level2, pre.level2 {
 padding-left: 6em;
}

span.emphasis {
 font-style: italic;
}

span.bold {
 font-weight: bold;
}

span.manpage {
 font-weight: bold;
}

h2.nroffsh {
 background-color: #e0e0e0;
}

span.nroffip {
 font-weight: bold;
 font-size: 120%;
 font-family: monospace;
}

p.roffit {
 text-align: center;
 font-size: 80%;
}
</STYLE>
</head><body>
<a name="NAME"></a><h2 class="nroffsh">Name</h2>
<p class="level0">pcap_open_live - open a device for capturing </p><a name="SYNOPSIS"></a><h2 class="nroffsh">Synopsis</h2><pre class="level0">
&#35;include &lt;pcap/pcap.h&gt;
char errbuf[PCAP_ERRBUF_SIZE];
pcap_t *pcap_open_live(const char *device, int snaplen,
&nbsp;   int promisc, int to_ms, char *errbuf);
</pre>
<a name="DESCRIPTION"></a><h2 class="nroffsh">Description</h2>
<p class="level0"><span Class="bold">pcap_open_live</span>() is used to obtain a packet capture handle to look at packets on the network. <span Class="emphasis">device</span> is a string that specifies the network device to open; on all supported Linux systems, as well as on recent versions of macOS and Solaris, a <span Class="emphasis">device</span> argument of &quot;any&quot; or <span Class="bold">NULL</span> can be used to capture packets from all network interfaces.  The latter should not be confused with all available capture devices as seen by <a Class="bold" href="./pcap_findalldevs.html">pcap_findalldevs</a>(3PCAP), which may also include D-Bus, USB etc. </p>
<p class="level0"><span Class="emphasis">snaplen</span> specifies the snapshot length to be set on the handle.  If the packet data should not be truncated at the end, a value of 262144 should be sufficient for most devices, but D-Bus devices require a value of 128MB (128*1024*1024). </p>
<p class="level0"><span Class="emphasis">promisc</span> specifies whether the interface is to be put into promiscuous mode. If <span Class="emphasis">promisc</span> is non-zero, promiscuous mode will be set, otherwise it will not be set. </p>
<p class="level0"><span Class="emphasis">to_ms</span> specifies the packet buffer timeout, as a non-negative value, in milliseconds.  (See <a Class="bold" href="./pcap.html">pcap</a>(3PCAP) for an explanation of the packet buffer timeout.) </p>
<p class="level0"><span Class="emphasis">errbuf</span> is a buffer large enough to hold at least <span Class="bold">PCAP_ERRBUF_SIZE</span> chars. </p><a name="RETURN"></a><h2 class="nroffsh">Return value</h2>
<p class="level0"><span Class="bold">pcap_open_live</span>() returns a <span Class="bold">pcap_t *</span> on success and <span Class="bold">NULL</span> on failure. If <span Class="bold">NULL</span> is returned, <span Class="emphasis">errbuf</span> is filled in with an appropriate error message. <span Class="emphasis">errbuf</span> may also be set to warning text when <span Class="bold">pcap_open_live</span>() succeeds; to detect this case the caller should store a zero-length string in <span Class="emphasis">errbuf</span> before calling <span Class="bold">pcap_open_live</span>() and display the warning to the user if <span Class="emphasis">errbuf</span> is no longer a zero-length string. </p><a name="SEE"></a><h2 class="nroffsh">See also</h2>
<p class="level0"><a Class="bold" href="./pcap_create.html">pcap_create</a>(3PCAP), <a Class="bold" href="./pcap_activate.html">pcap_activate</a>(3PCAP) </p><p class="roffit">
 This HTML page was made with <a href="https://daniel.haxx.se/projects/roffit/">roffit</a>.
</body></html>
