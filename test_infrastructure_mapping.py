#!/usr/bin/env python3
"""
Test script to demonstrate infrastructure domain mapping.
"""

import socket
import requests
import time

def get_ip_for_domain(domain):
    """Get IP address for a domain"""
    try:
        ip = socket.gethostbyname(domain)
        return ip
    except socket.gaierror:
        return None

def test_infrastructure_mapping():
    """Test how different domains resolve and what their infrastructure looks like"""
    test_domains = [
        "google.com",
        "www.google.com",
        "youtube.com", 
        "www.youtube.com",
        "facebook.com",
        "www.facebook.com",
        "twitter.com",
        "www.twitter.com"
    ]
    
    print("Infrastructure Domain Mapping Test")
    print("=================================")
    print("This shows how user domains resolve to infrastructure domains")
    
    for domain in test_domains:
        print(f"\nTesting {domain}:")
        
        # Get IP
        ip = get_ip_for_domain(domain)
        if not ip:
            print(f"  Could not resolve {domain}")
            continue
            
        print(f"  Resolves to IP: {ip}")
        
        # Try reverse DNS (simulated - the Rust app will do the actual lookup)
        print(f"  Reverse DNS will be performed by Rust application")
        print(f"  Expected infrastructure domains:")
        
        if 'google' in domain or 'youtube' in domain:
            print(f"    - Likely: *.1e100.net (Google infrastructure)")
            print(f"    - Should map to: google.com or youtube.com")
        elif 'facebook' in domain:
            print(f"    - Likely: *.facebook.com or *.fbcdn.net")
            print(f"    - Should map to: facebook.com")
        elif 'twitter' in domain:
            print(f"    - Likely: *.twitter.com or *.twimg.com")
            print(f"    - Should map to: twitter.com")

def test_with_requests():
    """Make requests to see the infrastructure mapping in action"""
    print("\n" + "="*60)
    print("Making test requests to see infrastructure mapping...")
    print("Watch the Rust application console for:")
    print("  1. 'Reverse DNS detection: IP -> infrastructure.domain'")
    print("  2. 'Mapped to user domain: infrastructure.domain -> user.domain'")
    print("  3. 'BLOCKED' messages with both domains shown")
    
    test_urls = [
        "https://www.google.com",
        "https://www.youtube.com",
        "https://youtube.com",
        "https://httpbin.org/get"  # This should not be blocked
    ]
    
    for url in test_urls:
        try:
            print(f"\nMaking request to: {url}")
            response = requests.get(url, timeout=10)
            print(f"  Status: {response.status_code}")
            print("  Check Rust console for infrastructure mapping...")
        except Exception as e:
            print(f"  Error: {e}")
        
        time.sleep(3)  # Wait between requests

def explain_infrastructure_domains():
    """Explain what infrastructure domains are"""
    print("\n" + "="*60)
    print("UNDERSTANDING INFRASTRUCTURE DOMAINS")
    print("="*60)
    print("""
When you visit google.com, here's what happens:

1. DNS resolves google.com to an IP (e.g., **************)
2. Reverse DNS of that IP gives infrastructure name (e.g., lga25s78-in-f3.1e100.net)
3. This infrastructure name tells us:
   - lga25s78: Server identifier (LaGuardia area, server 78)
   - 1e100.net: Google's infrastructure domain (1e100 = 10^100 = googol)

Common infrastructure domains:
- *.1e100.net → Google/YouTube services
- *.googleusercontent.com → Google content
- *.googlevideo.com → YouTube videos
- *.fbcdn.net → Facebook content delivery
- *.twimg.com → Twitter images

The Rust application now maps these back to user-facing domains!
""")

if __name__ == "__main__":
    print("Infrastructure Domain Mapping Test")
    print("This demonstrates how infrastructure domains are mapped to user domains.")
    print("Make sure the Rust application is running with admin privileges!")
    
    explain_infrastructure_domains()
    
    input("Press Enter to test domain resolution...")
    test_infrastructure_mapping()
    
    input("\nPress Enter to make test requests and see mapping in action...")
    test_with_requests()
    
    print("\nTest completed. The Rust application should now show:")
    print("1. Original infrastructure domains from reverse DNS")
    print("2. Mapped user-facing domains") 
    print("3. Proper blocking based on user domains")
