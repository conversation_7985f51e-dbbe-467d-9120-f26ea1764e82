# Internet Filter Windows Client

A Rust-based packet capture application that monitors outgoing HTTP/HTTPS requests and sends desktop notifications when blocked websites are accessed.

## Features

- **Real-time packet capture**: Monitors network traffic using WinPcap/Npcap
- **HTTP request filtering**: Parses HTTP Host headers from outgoing requests
- **HTTPS SNI parsing**: Extracts hostnames from TLS Server Name Indication
- **Hosts file filtering**: Compares requests against a configurable blocked hosts list
- **Desktop notifications**: Shows notifications when blocked sites are accessed
- **System tray integration**: Runs in the background with tray icon

## Prerequisites

1. **WinPcap or Npcap**: Required for packet capture on Windows
   - Download Npcap from: https://nmap.org/npcap/
   - Install with "WinPcap API-compatible mode" enabled

2. **Administrator privileges**: Required for packet capture

## Setup

1. Clone or download this project
2. Install Rust if not already installed: https://rustup.rs/
3. Build the project:
   ```bash
   cargo build --release
   ```

## Configuration

### Hosts File

Create or edit `blocked_hosts.txt` in the project root directory. Format:

```
# Comments start with #
127.0.0.1 facebook.com
127.0.0.1 www.facebook.com
127.0.0.1 youtube.com
127.0.0.1 www.youtube.com
```

Each line should contain an IP address followed by the hostname to block.

### Network Interface

The application currently uses a hardcoded network interface. You may need to update the device name in `src/main.rs`:

```rust
let mut cap = pcap::Capture::from_device("\\Device\\NPF_{YOUR-DEVICE-ID}")
```

To find your device ID, run the application once and check the console output for available devices.

## Usage

1. Run as administrator:
   ```bash
   cargo run
   ```

2. The application will:
   - Load the blocked hosts from `blocked_hosts.txt`
   - Start capturing packets
   - Monitor HTTP/HTTPS requests
   - Show desktop notifications for blocked sites
   - Display a system tray icon

3. To quit, right-click the tray icon and select "Quit"

## How It Works

1. **Packet Capture**: Uses the `pcap` crate to capture network packets
2. **Protocol Parsing**: Uses `pnet` to parse Ethernet, IP, and TCP layers
3. **HTTP Parsing**: Extracts Host headers from HTTP requests (port 80)
4. **HTTPS/TLS Parsing**: Extracts hostnames from TLS SNI extensions (port 443)
5. **Filtering**: Compares extracted hostnames against the blocked hosts list
6. **Notifications**: Uses `notify-rust` to show desktop notifications

## Limitations

- Requires administrator privileges
- Only monitors TCP traffic on ports 80 (HTTP) and 443 (HTTPS)
- HTTPS monitoring relies on SNI, which may not be present in all connections
- Cannot monitor encrypted traffic without SNI
- Currently hardcoded to a specific network interface

## Dependencies

- `pcap`: Packet capture
- `pnet`: Network packet parsing
- `notify-rust`: Desktop notifications
- `tray-item`: System tray integration

## Normal Behavior

- The application will show "Heartbeat" messages every minute to confirm it's running
- During periods of low network activity, the application may appear quiet - this is normal
- The application filters only HTTP (port 80) and HTTPS (port 443) traffic

## Troubleshooting

1. **"No device available"**: Install Npcap or WinPcap
2. **Permission denied**: Run as administrator
3. **No notifications**: Check Windows notification settings
4. **No packets captured**: Verify network interface ID in code
5. **Program appears to stop**: Check for heartbeat messages in console - if they stop, the program has exited
