<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
 "http://www.w3.org/TR/html4/loose.dtd">
<html><head>
<title>3PCAP man page</title>
<meta name="generator" content="roffit">
<STYLE type="text/css">
pre {
  overflow: auto;
  margin: 0;
}

P.level0, pre.level0 {
 padding-left: 2em;
}

P.level1, pre.level1 {
 padding-left: 4em;
}

P.level2, pre.level2 {
 padding-left: 6em;
}

span.emphasis {
 font-style: italic;
}

span.bold {
 font-weight: bold;
}

span.manpage {
 font-weight: bold;
}

h2.nroffsh {
 background-color: #e0e0e0;
}

span.nroffip {
 font-weight: bold;
 font-size: 120%;
 font-family: monospace;
}

p.roffit {
 text-align: center;
 font-size: 80%;
}
</STYLE>
</head><body>
<a name="NAME"></a><h2 class="nroffsh">Name</h2>
<p class="level0">pcap_set_tstamp_type - set the time stamp type to be used by a capture device </p><a name="SYNOPSIS"></a><h2 class="nroffsh">Synopsis</h2><pre class="level0">
&#35;include &lt;pcap/pcap.h&gt;
int pcap_set_tstamp_type(pcap_t *p, int tstamp_type);
</pre>
<a name="DESCRIPTION"></a><h2 class="nroffsh">Description</h2>
<p class="level0"><span Class="bold">pcap_set_tstamp_type</span>() sets the type of time stamp desired for packets captured on the pcap descriptor to the type specified by <span Class="emphasis">tstamp_type</span>. It must be called on a pcap descriptor created by <a Class="bold" href="./pcap_create.html">pcap_create</a>(3PCAP) that has not yet been activated by <a Class="bold" href="./pcap_activate.html">pcap_activate</a>(3PCAP). <a Class="bold" href="./pcap_list_tstamp_types.html">pcap_list_tstamp_types</a>(3PCAP) will give a list of the time stamp types supported by a given capture device. See <span Class="bold">\%pcap-tstamp</span>(7) for a list of all the time stamp types. </p><a name="RETURN"></a><h2 class="nroffsh">Return value</h2>
<p class="level0"><span Class="bold">pcap_set_tstamp_type</span>() returns <span Class="bold">0</span> on success if the specified time stamp type is expected to be supported by the capture device, <span Class="bold">PCAP_WARNING_TSTAMP_TYPE_NOTSUP</span> if the specified time stamp type is not supported by the capture device, <span Class="bold">PCAP_ERROR_ACTIVATED</span> if called on a capture handle that has been activated, and <span Class="bold">PCAP_ERROR_CANTSET_TSTAMP_TYPE</span> if the capture device doesn&#39;t support setting the time stamp type (only older versions of libpcap will return that; newer versions will always allow the time stamp type to be set to the default type). </p><a name="BACKWARD"></a><h2 class="nroffsh">Backward compatibility</h2>
<p class="level0">This function became available in libpcap release 1.2.1.  In previous releases, the time stamp type cannot be set; only the default time stamp type offered by a capture source is available. </p><a name="SEE"></a><h2 class="nroffsh">See also</h2>
<p class="level0"><a Class="bold" href="./pcap.html">pcap</a>(3PCAP), <a Class="bold" href="./pcap_tstamp_type_name_to_val.html">pcap_tstamp_type_name_to_val</a>(3PCAP) </p><p class="roffit">
 This HTML page was made with <a href="https://daniel.haxx.se/projects/roffit/">roffit</a>.
</body></html>
