#!/usr/bin/env python3
"""
Test script to verify reverse DNS functionality by resolving IPs and making requests.
"""

import socket
import subprocess
import requests
import time

def get_ip_for_domain(domain):
    """Get IP address for a domain"""
    try:
        ip = socket.gethostbyname(domain)
        return ip
    except socket.gaierror:
        return None

def reverse_dns_lookup(ip):
    """Perform reverse DNS lookup using nslookup"""
    try:
        # Create reverse IP format
        octets = ip.split('.')
        reverse_ip = f"{octets[3]}.{octets[2]}.{octets[1]}.{octets[0]}.in-addr.arpa"
        
        # Run nslookup
        result = subprocess.run(['nslookup', reverse_ip], 
                              capture_output=True, text=True, timeout=10)
        
        # Parse output
        for line in result.stdout.split('\n'):
            if 'name =' in line:
                hostname = line.split('name =')[1].strip().rstrip('.')
                return hostname
        
        return None
    except Exception as e:
        print(f"Error in reverse DNS lookup: {e}")
        return None

def test_reverse_dns():
    """Test reverse DNS lookups for various domains"""
    test_domains = [
        "youtube.com",
        "www.youtube.com", 
        "google.com",
        "www.google.com",
        "facebook.com",
        "www.facebook.com",
        "example.com"
    ]
    
    print("Reverse DNS Test")
    print("================")
    
    for domain in test_domains:
        print(f"\nTesting {domain}:")
        
        # Get IP
        ip = get_ip_for_domain(domain)
        if not ip:
            print(f"  Could not resolve {domain}")
            continue
            
        print(f"  Forward DNS: {domain} -> {ip}")
        
        # Try reverse DNS
        reverse_hostname = reverse_dns_lookup(ip)
        if reverse_hostname:
            print(f"  Reverse DNS: {ip} -> {reverse_hostname}")
        else:
            print(f"  Reverse DNS: {ip} -> No result")

def test_with_requests():
    """Make actual requests to trigger the Rust application"""
    print("\n" + "="*50)
    print("Making test requests...")
    print("Watch the Rust application for reverse DNS detections!")
    
    test_urls = [
        "https://www.youtube.com",
        "https://youtube.com", 
        "https://www.google.com",
        "https://httpbin.org/get"  # This shouldn't be blocked
    ]
    
    for url in test_urls:
        try:
            print(f"\nMaking request to: {url}")
            response = requests.get(url, timeout=10)
            print(f"  Status: {response.status_code}")
        except Exception as e:
            print(f"  Error: {e}")
        
        time.sleep(3)  # Wait between requests

if __name__ == "__main__":
    print("Reverse DNS Test Script")
    print("This will test reverse DNS lookups and make requests to trigger detection.")
    print("Make sure the Rust application is running with admin privileges!")
    
    input("Press Enter to start reverse DNS tests...")
    test_reverse_dns()
    
    input("\nPress Enter to make test requests...")
    test_with_requests()
    
    print("\nTest completed. Check the Rust application console for reverse DNS detections.")
