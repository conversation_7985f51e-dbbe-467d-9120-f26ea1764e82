<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
 "http://www.w3.org/TR/html4/loose.dtd">
<html><head>
<title>3PCAP man page</title>
<meta name="generator" content="roffit">
<STYLE type="text/css">
pre {
  overflow: auto;
  margin: 0;
}

P.level0, pre.level0 {
 padding-left: 2em;
}

P.level1, pre.level1 {
 padding-left: 4em;
}

P.level2, pre.level2 {
 padding-left: 6em;
}

span.emphasis {
 font-style: italic;
}

span.bold {
 font-weight: bold;
}

span.manpage {
 font-weight: bold;
}

h2.nroffsh {
 background-color: #e0e0e0;
}

span.nroffip {
 font-weight: bold;
 font-size: 120%;
 font-family: monospace;
}

p.roffit {
 text-align: center;
 font-size: 80%;
}
</STYLE>
</head><body>
<a name="NAME"></a><h2 class="nroffsh">Name</h2>
<p class="level0">pcap_compile - compile a filter expression </p><a name="SYNOPSIS"></a><h2 class="nroffsh">Synopsis</h2><pre class="level0">
&#35;include &lt;pcap/pcap.h&gt;
int pcap_compile(pcap_t *p, struct bpf_program *fp,
&nbsp;   const char *str, int optimize, bpf_u_int32 netmask);
</pre>
<a name="DESCRIPTION"></a><h2 class="nroffsh">Description</h2>
<p class="level0"><span Class="bold">pcap_compile</span>() is used to compile the string <span Class="emphasis">str</span> into a filter program.  See <span Class="bold">\%pcap-filter</span>(7) for the syntax of that string. <span Class="emphasis">fp</span> is a pointer to a <span Class="emphasis">bpf_program</span> struct and is filled in by <span Class="bold">pcap_compile</span>(). <span Class="emphasis">optimize</span> controls whether optimization on the resulting code is performed. <span Class="emphasis">netmask</span> specifies the IPv4 netmask of the network on which packets are being captured; it is used only when checking for IPv4 broadcast addresses in the filter program.  If the netmask of the network on which packets are being captured isn&#39;t known to the program, or if packets are being captured on the Linux &quot;any&quot; pseudo-interface that can capture on more than one network, a value of <span Class="bold">PCAP_NETMASK_UNKNOWN</span> can be supplied; tests for IPv4 broadcast addresses will fail to compile, but all other tests in the filter program will be OK. </p>
<p class="level0">NOTE: in libpcap 1.8.0 and later, <span Class="bold">pcap_compile</span>() can be used in multiple threads within a single process.  However, in earlier versions of libpcap, it is <span Class="emphasis">not</span> safe to use <span Class="bold">pcap_compile</span>() in multiple threads in a single process without some form of mutual exclusion allowing only one thread to call it at any given time. </p><a name="RETURN"></a><h2 class="nroffsh">Return value</h2>
<p class="level0"><span Class="bold">pcap_compile</span>() returns <span Class="bold">0</span> on success and <span Class="bold">PCAP_ERROR</span> on failure. If <span Class="bold">PCAP_ERROR</span> is returned, <a Class="bold" href="./pcap_geterr.html">pcap_geterr</a>(3PCAP) or <span Class="bold">pcap_perror</span>(3PCAP) may be called with <span Class="emphasis">p</span> as an argument to fetch or display the error text. </p><a name="BACKWARD"></a><h2 class="nroffsh">Backward compatibility</h2>
<p class="level0">The <span Class="bold">PCAP_NETMASK_UNKNOWN</span> constant became available in libpcap release 1.1.0. </p><a name="SEE"></a><h2 class="nroffsh">See also</h2>
<p class="level0"><a Class="bold" href="./pcap.html">pcap</a>(3PCAP), <a Class="bold" href="./pcap_setfilter.html">pcap_setfilter</a>(3PCAP), <a Class="bold" href="./pcap_freecode.html">pcap_freecode</a>(3PCAP) </p><p class="roffit">
 This HTML page was made with <a href="https://daniel.haxx.se/projects/roffit/">roffit</a>.
</body></html>
