<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
 "http://www.w3.org/TR/html4/loose.dtd">
<html><head>
<title>3PCAP man page</title>
<meta name="generator" content="roffit">
<STYLE type="text/css">
pre {
  overflow: auto;
  margin: 0;
}

P.level0, pre.level0 {
 padding-left: 2em;
}

P.level1, pre.level1 {
 padding-left: 4em;
}

P.level2, pre.level2 {
 padding-left: 6em;
}

span.emphasis {
 font-style: italic;
}

span.bold {
 font-weight: bold;
}

span.manpage {
 font-weight: bold;
}

h2.nroffsh {
 background-color: #e0e0e0;
}

span.nroffip {
 font-weight: bold;
 font-size: 120%;
 font-family: monospace;
}

p.roffit {
 text-align: center;
 font-size: 80%;
}
</STYLE>
</head><body>
<a name="NAME"></a><h2 class="nroffsh">Name</h2>
<p class="level0">pcap_file - get the standard I/O stream for a savefile being read </p><a name="SYNOPSIS"></a><h2 class="nroffsh">Synopsis</h2><pre class="level0">
&#35;include &lt;pcap/pcap.h&gt;
FILE *pcap_file(pcap_t *p);
</pre>
<a name="DESCRIPTION"></a><h2 class="nroffsh">Description</h2>
<p class="level0"><span Class="bold">pcap_file</span>() returns the standard I/O stream of the ``savefile,&#39;&#39; if a ``savefile&#39;&#39; was opened with <a Class="bold" href="./pcap_open_offline.html">pcap_open_offline</a>(3PCAP), or <span Class="bold">NULL</span>, if a network device was opened with <a Class="bold" href="./pcap_create.html">pcap_create</a>(3PCAP) and <span Class="bold">\%pcap_activate</span>(3PCAP), or with <a Class="bold" href="./pcap_open_live.html">pcap_open_live</a>(3PCAP). </p>
<p class="level0">Note that the Packet Capture library is usually built with large file support, so the standard I/O stream of the ``savefile&#39;&#39; might refer to a file larger than 2 gigabytes; applications that use <span Class="bold">pcap_file</span>() should, if possible, use calls that support large files on the return value of <span Class="bold">pcap_file</span>() or the value returned by <span Class="bold">fileno</span>(3) when passed the return value of <span Class="bold">pcap_file</span>(). </p><a name="SEE"></a><h2 class="nroffsh">See also</h2>
<p class="level0"><a Class="bold" href="./pcap.html">pcap</a>(3PCAP) </p><p class="roffit">
 This HTML page was made with <a href="https://daniel.haxx.se/projects/roffit/">roffit</a>.
</body></html>
