<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
 "http://www.w3.org/TR/html4/loose.dtd">
<html><head>
<title>3PCAP man page</title>
<meta name="generator" content="roffit">
<STYLE type="text/css">
pre {
  overflow: auto;
  margin: 0;
}

P.level0, pre.level0 {
 padding-left: 2em;
}

P.level1, pre.level1 {
 padding-left: 4em;
}

P.level2, pre.level2 {
 padding-left: 6em;
}

span.emphasis {
 font-style: italic;
}

span.bold {
 font-weight: bold;
}

span.manpage {
 font-weight: bold;
}

h2.nroffsh {
 background-color: #e0e0e0;
}

span.nroffip {
 font-weight: bold;
 font-size: 120%;
 font-family: monospace;
}

p.roffit {
 text-align: center;
 font-size: 80%;
}
</STYLE>
</head><body>
<a name="NAME"></a><h2 class="nroffsh">Name</h2>
<p class="level0">pcap_breakloop - force a pcap_dispatch() or pcap_loop() call to return </p><a name="SYNOPSIS"></a><h2 class="nroffsh">Synopsis</h2><pre class="level0">
&#35;include &lt;pcap/pcap.h&gt;
void pcap_breakloop(pcap_t *);
</pre>
<a name="DESCRIPTION"></a><h2 class="nroffsh">Description</h2>
<p class="level0"><span Class="bold">pcap_breakloop</span>() sets a flag that will force <span Class="bold">pcap_dispatch</span>(3PCAP) or <a Class="bold" href="./pcap_loop.html">pcap_loop</a>(3PCAP) to return rather than looping; they will return the number of packets that have been processed so far, or <span Class="bold">PCAP_ERROR_BREAK</span> if no packets have been processed so far.  If the loop is currently blocked waiting for packets to arrive, <span Class="bold">pcap_breakloop</span>() will also, on some platforms, wake up the thread that is blocked.  In this version of libpcap, the only platforms on which a wakeup is caused by <span Class="bold">pcap_breakloop</span>() are Linux and Windows, and the wakeup will only be caused when capturing on network interfaces; it will not be caused on other operating systems, and will not be caused on any OS when capturing on other types of devices. </p>
<p class="level0">This routine is safe to use inside a signal handler on UNIX or a console control handler on Windows, or in a thread other than the one in which the loop is running, as it merely sets a flag that is checked within the loop and, on some platforms, performs a signal-safe and thread-safe API call. </p>
<p class="level0">The flag is checked in loops reading packets from the OS - a signal by itself will not necessarily terminate those loops - as well as in loops processing a set of packets returned by the OS. Note that if you are catching signals on UNIX systems that support restarting system calls after a signal, and calling pcap_breakloop() in the signal handler, you must specify, when catching those signals, that system calls should NOT be restarted by that signal.  Otherwise, if the signal interrupted a call reading packets in a live capture, when your signal handler returns after calling pcap_breakloop(), the call will be restarted, and the loop will not terminate until more packets arrive and the call completes. </p>
<p class="level0">Note also that, in a multi-threaded application, if one thread is blocked in pcap_dispatch(), pcap_loop(), pcap_next(3PCAP), or pcap_next_ex(3PCAP), a call to pcap_breakloop() in a different thread will only unblock that thread on the platforms and capture devices listed above. </p>
<p class="level0">If a non-zero packet buffer timeout is set on the <span Class="bold">pcap_t</span>, and you are capturing on a network interface, the thread will be unblocked with the timeout expires.  This is not guaranteed to happen unless at least one packet has arrived; the only platforms on which it happens are macOS, the BSDs, Solaris 11, AIX, Tru64 UNIX, and Windows. </p>
<p class="level0">If you want to ensure that the loop will eventually be unblocked on any other platforms, or unblocked when capturing on a device other than a network interface, you will need to use whatever mechanism the OS provides for breaking a thread out of blocking calls in order to unblock the thread, such as thread cancellation or thread signalling in systems that support POSIX threads. </p>
<p class="level0">Note that if pcap_breakloop() unblocks the thread capturing packets, and you are running on a platform that supports packet buffering, there may be packets in the buffer that arrived before pcap_breakloop() were called but that weren&#39;t yet provided to libpcap, those packets will not have been processed by pcap_dispatch() or pcap_loop().  If pcap_breakloop() was called in order to terminate the capture process, then, in order to process those packets, you would have to call pcap_dispatch() one time in order to process the last batch of packets. This may block until the packet buffer timeout expires, so a non-zero packet buffer timeout must be used. </p>
<p class="level0">Note that <span Class="bold">pcap_next</span>() and <span Class="bold">pcap_next_ex</span>() will, on some platforms, loop reading packets from the OS; that loop will not necessarily be terminated by a signal, so <span Class="bold">pcap_breakloop</span>() should be used to terminate packet processing even if <span Class="bold">pcap_next</span>() or <span Class="bold">pcap_next_ex</span>() is being used. </p>
<p class="level0"><span Class="bold">pcap_breakloop</span>() does not guarantee that no further packets will be processed by <span Class="bold">pcap_dispatch</span>() or <span Class="bold">pcap_loop</span>() after it is called; at most one more packet might be processed. </p>
<p class="level0">If <span Class="bold">PCAP_ERROR_BREAK</span> is returned from <span Class="bold">pcap_dispatch</span>() or <span Class="bold">pcap_loop</span>(), the flag is cleared, so a subsequent call will resume reading packets. If a positive number is returned, the flag is not cleared, so a subsequent call will return <span Class="bold">PCAP_ERROR_BREAK</span> and clear the flag. </p><a name="BACKWARD"></a><h2 class="nroffsh">Backward compatibility</h2>
<p class="level0">This function became available in libpcap release 0.8.1. </p>
<p class="level0">In releases prior to libpcap 1.10.0, <span Class="bold">pcap_breakloop</span>() will not wake up a blocked thread on any platform. </p><a name="SEE"></a><h2 class="nroffsh">See also</h2>
<p class="level0"><a Class="bold" href="./pcap.html">pcap</a>(3PCAP) </p><p class="roffit">
 This HTML page was made with <a href="https://daniel.haxx.se/projects/roffit/">roffit</a>.
</body></html>
