<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
 "http://www.w3.org/TR/html4/loose.dtd">
<html><head>
<title>3PCAP man page</title>
<meta name="generator" content="roffit">
<STYLE type="text/css">
pre {
  overflow: auto;
  margin: 0;
}

P.level0, pre.level0 {
 padding-left: 2em;
}

P.level1, pre.level1 {
 padding-left: 4em;
}

P.level2, pre.level2 {
 padding-left: 6em;
}

span.emphasis {
 font-style: italic;
}

span.bold {
 font-weight: bold;
}

span.manpage {
 font-weight: bold;
}

h2.nroffsh {
 background-color: #e0e0e0;
}

span.nroffip {
 font-weight: bold;
 font-size: 120%;
 font-family: monospace;
}

p.roffit {
 text-align: center;
 font-size: 80%;
}
</STYLE>
</head><body>
<a name="NAME"></a><h2 class="nroffsh">Name</h2>
<p class="level0">pcap_set_timeout - set the packet buffer timeout for a not-yet-activated capture handle </p><a name="SYNOPSIS"></a><h2 class="nroffsh">Synopsis</h2><pre class="level0">
&#35;include &lt;pcap/pcap.h&gt;
int pcap_set_timeout(pcap_t *p, int to_ms);
</pre>
<a name="DESCRIPTION"></a><h2 class="nroffsh">Description</h2>
<p class="level0"><span Class="bold">pcap_set_timeout</span>() sets the packet buffer timeout that will be used on a capture handle when the handle is activated to <span Class="emphasis">to_ms</span>, which is in units of milliseconds.  (See <a Class="bold" href="./pcap.html">pcap</a>(3PCAP) for an explanation of the packet buffer timeout.) </p>
<p class="level0">The behavior, if the timeout isn&#39;t specified, is undefined, as is the behavior if the timeout is set to zero or to a negative value.  We recommend always setting the timeout to a non-zero value unless immediate mode is set, in which case the timeout has no effect. </p><a name="RETURN"></a><h2 class="nroffsh">Return value</h2>
<p class="level0"><span Class="bold">pcap_set_timeout</span>() returns <span Class="bold">0</span> on success or <span Class="bold">PCAP_ERROR_ACTIVATED</span> if called on a capture handle that has been activated. </p><a name="SEE"></a><h2 class="nroffsh">See also</h2>
<p class="level0"><a Class="bold" href="./pcap_create.html">pcap_create</a>(3PCAP), <a Class="bold" href="./pcap_activate.html">pcap_activate</a>(3PCAP), <span Class="bold">\%pcap_set_immediate_mode</span>(3PCAP) </p><p class="roffit">
 This HTML page was made with <a href="https://daniel.haxx.se/projects/roffit/">roffit</a>.
</body></html>
