#!/usr/bin/env python3
"""
Simple test script to generate a single HTTP request to a blocked site.
Use this to test if the filter is working and if notifications are sent.
"""

import requests
import time

def test_blocked_site():
    """Test a single request to a blocked site"""
    print("Testing HTTP request to example.com (should be blocked)")
    try:
        response = requests.get("http://example.com", timeout=5)
        print(f"Request completed with status: {response.status_code}")
    except Exception as e:
        print(f"Request failed: {e}")

if __name__ == "__main__":
    print("Simple Internet Filter Test")
    print("Make sure the Rust application is running with admin privileges!")
    print("This will make ONE request to example.com")
    
    input("Press Enter to continue...")
    test_blocked_site()
    print("Test completed. Check the Rust application console for output.")
