<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
 "http://www.w3.org/TR/html4/loose.dtd">
<html><head>
<title>3PCAP man page</title>
<meta name="generator" content="roffit">
<STYLE type="text/css">
pre {
  overflow: auto;
  margin: 0;
}

P.level0, pre.level0 {
 padding-left: 2em;
}

P.level1, pre.level1 {
 padding-left: 4em;
}

P.level2, pre.level2 {
 padding-left: 6em;
}

span.emphasis {
 font-style: italic;
}

span.bold {
 font-weight: bold;
}

span.manpage {
 font-weight: bold;
}

h2.nroffsh {
 background-color: #e0e0e0;
}

span.nroffip {
 font-weight: bold;
 font-size: 120%;
 font-family: monospace;
}

p.roffit {
 text-align: center;
 font-size: 80%;
}
</STYLE>
</head><body>
<a name="NAME"></a><h2 class="nroffsh">Name</h2>
<p class="level0">pcap_set_protocol_linux - set capture protocol for a not-yet-activated capture handle </p><a name="SYNOPSIS"></a><h2 class="nroffsh">Synopsis</h2><pre class="level0">
&#35;include &lt;pcap/pcap.h&gt;
int pcap_set_protocol_linux(pcap_t *p, int protocol);
</pre>
<a name="DESCRIPTION"></a><h2 class="nroffsh">Description</h2>
<p class="level0">On network interface devices on Linux, <span Class="bold">pcap_set_protocol_linux</span>() sets the protocol to be used in the <span Class="bold">socket</span>(2) call to create a capture socket when the handle is activated.  The argument is a link-layer protocol value, such as the values in the <span Class="bold">&lt;linux/if_ether.h&gt;</span> header file, specified in host byte order. If <span Class="emphasis">protocol</span> is non-zero, packets of that protocol will be captured when the handle is activated, otherwise, all packets will be captured.  This function is only provided on Linux, and, if it is used on any device other than a network interface, it will have no effect. </p>
<p class="level0">It should not be used in portable code; instead, a filter should be specified with <a Class="bold" href="./pcap_setfilter.html">pcap_setfilter</a>(3PCAP). </p>
<p class="level0">If a given network interface provides a standard link-layer header, with a standard packet type, but provides some packet types with a different socket-layer protocol type from the one in the link-layer header, that packet type cannot be filtered with a filter specified with <span Class="bold">pcap_setfilter</span>() but can be filtered by specifying the socket-layer protocol type using <span Class="bold">pcap_set_protocol_linux</span>(). </p><a name="RETURN"></a><h2 class="nroffsh">Return value</h2>
<p class="level0"><span Class="bold">pcap_set_protocol_linux</span>() returns <span Class="bold">0</span> on success or <span Class="bold">PCAP_ERROR_ACTIVATED</span> if called on a capture handle that has been activated. </p><a name="BACKWARD"></a><h2 class="nroffsh">Backward compatibility</h2>
<p class="level0">This function became available in libpcap release 1.9.0. </p><a name="SEE"></a><h2 class="nroffsh">See also</h2>
<p class="level0"><a Class="bold" href="./pcap.html">pcap</a>(3PCAP), <a Class="bold" href="./pcap_create.html">pcap_create</a>(3PCAP), <a Class="bold" href="./pcap_activate.html">pcap_activate</a>(3PCAP) </p><p class="roffit">
 This HTML page was made with <a href="https://daniel.haxx.se/projects/roffit/">roffit</a>.
</body></html>
