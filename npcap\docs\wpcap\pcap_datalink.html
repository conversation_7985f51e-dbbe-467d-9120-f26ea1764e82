<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
 "http://www.w3.org/TR/html4/loose.dtd">
<html><head>
<title>3PCAP man page</title>
<meta name="generator" content="roffit">
<STYLE type="text/css">
pre {
  overflow: auto;
  margin: 0;
}

P.level0, pre.level0 {
 padding-left: 2em;
}

P.level1, pre.level1 {
 padding-left: 4em;
}

P.level2, pre.level2 {
 padding-left: 6em;
}

span.emphasis {
 font-style: italic;
}

span.bold {
 font-weight: bold;
}

span.manpage {
 font-weight: bold;
}

h2.nroffsh {
 background-color: #e0e0e0;
}

span.nroffip {
 font-weight: bold;
 font-size: 120%;
 font-family: monospace;
}

p.roffit {
 text-align: center;
 font-size: 80%;
}
</STYLE>
</head><body>
<a name="NAME"></a><h2 class="nroffsh">Name</h2>
<p class="level0">pcap_datalink - get the link-layer header type </p><a name="SYNOPSIS"></a><h2 class="nroffsh">Synopsis</h2><pre class="level0">
&#35;include &lt;pcap/pcap.h&gt;
int pcap_datalink(pcap_t *p);
</pre>
<a name="DESCRIPTION"></a><h2 class="nroffsh">Description</h2>
<p class="level0"><span Class="bold">pcap_datalink</span>() returns the link-layer header type for the live capture or ``savefile&#39;&#39; specified by <span Class="emphasis">p</span>. </p>
<p class="level0">It must not be called on a pcap descriptor created by <span Class="bold">\%pcap_create</span>(3PCAP) that has not yet been activated by <span Class="bold">\%pcap_activate</span>(3PCAP). </p>
<p class="level0"><span Class="emphasis"><a href="https://www.tcpdump.org/linktypes.html">https://www.tcpdump.org/linktypes.html</a></span> lists the values <span Class="bold">pcap_datalink</span>() can return and describes the packet formats that correspond to those values. </p>
<p class="level0">Do <span Class="bold">NOT</span> assume that the packets for a given capture or ``savefile`` will have any given link-layer header type, such as <span Class="bold">DLT_EN10MB</span> for Ethernet.  For example, the &quot;any&quot; device on Linux will have a link-layer header type of <span Class="bold">DLT_LINUX_SLL</span> or <span Class="bold">DLT_LINUX_SLL2</span> even if all devices on the system at the time the &quot;any&quot; device is opened have some other data link type, such as <span Class="bold">DLT_EN10MB</span> for Ethernet. </p><a name="RETURN"></a><h2 class="nroffsh">Return value</h2>
<p class="level0"><span Class="bold">pcap_datalink</span>() returns the link-layer header type on success and <span Class="bold">PCAP_ERROR_NOT_ACTIVATED</span> if called on a capture handle that has been created but not activated. </p><a name="SEE"></a><h2 class="nroffsh">See also</h2>
<p class="level0"><a Class="bold" href="./pcap.html">pcap</a>(3PCAP), <a Class="bold" href="./pcap-linktype.html">pcap-linktype</a>(7) </p><p class="roffit">
 This HTML page was made with <a href="https://daniel.haxx.se/projects/roffit/">roffit</a>.
</body></html>
