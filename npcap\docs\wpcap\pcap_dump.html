<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
 "http://www.w3.org/TR/html4/loose.dtd">
<html><head>
<title>3PCAP man page</title>
<meta name="generator" content="roffit">
<STYLE type="text/css">
pre {
  overflow: auto;
  margin: 0;
}

P.level0, pre.level0 {
 padding-left: 2em;
}

P.level1, pre.level1 {
 padding-left: 4em;
}

P.level2, pre.level2 {
 padding-left: 6em;
}

span.emphasis {
 font-style: italic;
}

span.bold {
 font-weight: bold;
}

span.manpage {
 font-weight: bold;
}

h2.nroffsh {
 background-color: #e0e0e0;
}

span.nroffip {
 font-weight: bold;
 font-size: 120%;
 font-family: monospace;
}

p.roffit {
 text-align: center;
 font-size: 80%;
}
</STYLE>
</head><body>
<a name="NAME"></a><h2 class="nroffsh">Name</h2>
<p class="level0">pcap_dump - write a packet to a capture file </p><a name="SYNOPSIS"></a><h2 class="nroffsh">Synopsis</h2><pre class="level0">
&#35;include &lt;pcap/pcap.h&gt;
void pcap_dump(u_char *user, struct pcap_pkthdr *h,
&nbsp;   u_char *sp);
</pre>
<a name="DESCRIPTION"></a><h2 class="nroffsh">Description</h2>
<p class="level0"><span Class="bold">pcap_dump</span>() outputs a packet to the ``savefile&#39;&#39; opened with <a Class="bold" href="./pcap_dump_open.html">pcap_dump_open</a>(3PCAP). Note that its calling arguments are suitable for use with <span Class="bold">pcap_dispatch</span>(3PCAP) or <a Class="bold" href="./pcap_loop.html">pcap_loop</a>(3PCAP). If called directly, the <span Class="emphasis">user</span> parameter is of type <span Class="bold">pcap_dumper_t</span> as returned by <span Class="bold">pcap_dump_open</span>(). </p><a name="SEE"></a><h2 class="nroffsh">See also</h2>
<p class="level0"><a Class="bold" href="./pcap.html">pcap</a>(3PCAP) </p><p class="roffit">
 This HTML page was made with <a href="https://daniel.haxx.se/projects/roffit/">roffit</a>.
</body></html>
