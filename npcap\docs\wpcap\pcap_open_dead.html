<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
 "http://www.w3.org/TR/html4/loose.dtd">
<html><head>
<title>3PCAP man page</title>
<meta name="generator" content="roffit">
<STYLE type="text/css">
pre {
  overflow: auto;
  margin: 0;
}

P.level0, pre.level0 {
 padding-left: 2em;
}

P.level1, pre.level1 {
 padding-left: 4em;
}

P.level2, pre.level2 {
 padding-left: 6em;
}

span.emphasis {
 font-style: italic;
}

span.bold {
 font-weight: bold;
}

span.manpage {
 font-weight: bold;
}

h2.nroffsh {
 background-color: #e0e0e0;
}

span.nroffip {
 font-weight: bold;
 font-size: 120%;
 font-family: monospace;
}

p.roffit {
 text-align: center;
 font-size: 80%;
}
</STYLE>
</head><body>
<a name="NAME"></a><h2 class="nroffsh">Name</h2>
<p class="level0">pcap_open_dead, pcap_open_dead_with_tstamp_precision - open a fake pcap_t for compiling filters or opening a capture for output </p><a name="SYNOPSIS"></a><h2 class="nroffsh">Synopsis</h2><pre class="level0">
&#35;include &lt;pcap/pcap.h&gt;
pcap_t *pcap_open_dead(int linktype, int snaplen);
pcap_t *pcap_open_dead_with_tstamp_precision(int linktype, int snaplen,
&nbsp;   u_int precision);
</pre>
<a name="DESCRIPTION"></a><h2 class="nroffsh">Description</h2>
<p class="level0"><span Class="bold">pcap_open_dead</span>() and <span Class="bold">pcap_open_dead_with_tstamp_precision</span>() are used for creating a <span Class="bold">pcap_t</span> structure to use when calling the other functions in libpcap.  It is typically used when just using libpcap for compiling BPF code; it can also be used if using <a Class="bold" href="./pcap_dump_open.html">pcap_dump_open</a>(3PCAP), <a Class="bold" href="./pcap_dump.html">pcap_dump</a>(3PCAP), and <a Class="bold" href="./pcap_dump_close.html">pcap_dump_close</a>(3PCAP) to write a savefile if there is no <span Class="bold">pcap_t</span> that supplies the packets to be written. </p>
<p class="level0"><span Class="emphasis">linktype</span> specifies the link-layer type for the <span Class="bold">pcap_t</span>. </p>
<p class="level0"><span Class="emphasis">snaplen</span> specifies the snapshot length for the <span Class="bold">pcap_t</span>. </p>
<p class="level0">When <span Class="bold">pcap_open_dead_with_tstamp_precision</span>(), is used to create a <span Class="bold">pcap_t</span> for use with <span Class="bold">pcap_dump_open</span>(), <span Class="emphasis">precision</span> specifies the time stamp precision for packets; <span Class="bold">PCAP_TSTAMP_PRECISION_MICRO</span> should be specified if the packets to be written have time stamps in seconds and microseconds, and <span Class="bold">PCAP_TSTAMP_PRECISION_NANO</span> should be specified if the packets to be written have time stamps in seconds and nanoseconds.  Its value does not affect <a Class="bold" href="./pcap_compile.html">pcap_compile</a>(3PCAP). </p><a name="BACKWARD"></a><h2 class="nroffsh">Backward compatibility</h2>
<p class="level0">The <span Class="bold">pcap_open_dead_with_tstamp_precision</span>() function became available in libpcap release 1.5.1.  In previous releases, there was no mechanism to open a savefile for writing with time stamps given in seconds and nanoseconds. </p><a name="SEE"></a><h2 class="nroffsh">See also</h2>
<p class="level0"><a Class="bold" href="./pcap.html">pcap</a>(3PCAP), <span Class="bold">\%pcap-linktype</span>(7) </p><p class="roffit">
 This HTML page was made with <a href="https://daniel.haxx.se/projects/roffit/">roffit</a>.
</body></html>
