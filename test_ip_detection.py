#!/usr/bin/env python3
"""
Test script to verify IP-based detection by making requests to known services.
"""

import requests
import socket
import time

def get_ip_for_domain(domain):
    """Get IP address for a domain"""
    try:
        ip = socket.gethostbyname(domain)
        return ip
    except socket.gaierror:
        return None

def test_ip_detection():
    """Test IP-based detection"""
    test_domains = [
        "youtube.com",
        "www.youtube.com", 
        "facebook.com",
        "www.facebook.com",
        "twitter.com",
        "www.twitter.com",
        "google.com",
        "www.google.com"
    ]
    
    print("IP Detection Test")
    print("=================")
    
    for domain in test_domains:
        ip = get_ip_for_domain(domain)
        if ip:
            print(f"{domain} -> {ip}")
        else:
            print(f"{domain} -> Could not resolve")
    
    print("\nMaking test requests...")
    print("Watch the Rust application for IP-based detections!")
    
    # Make some actual requests to trigger the detection
    test_urls = [
        "https://www.youtube.com",
        "https://www.google.com", 
        "https://httpbin.org/get"  # This shouldn't be detected
    ]
    
    for url in test_urls:
        try:
            print(f"\nMaking request to: {url}")
            response = requests.get(url, timeout=10)
            print(f"  Status: {response.status_code}")
        except Exception as e:
            print(f"  Error: {e}")
        
        time.sleep(3)  # Wait between requests

if __name__ == "__main__":
    print("IP-based Detection Test Script")
    print("Make sure the Rust application is running with admin privileges!")
    print("This will show IP addresses for domains and make test requests.")
    
    input("Press Enter to continue...")
    test_ip_detection()
    print("\nTest completed. Check the Rust application console for IP-based detections.")
