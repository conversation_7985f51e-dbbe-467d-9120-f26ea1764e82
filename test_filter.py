#!/usr/bin/env python3
"""
Simple test script to generate HTTP requests for testing the filter.
Run this while the Rust application is running to test the filtering.
"""

import requests
import time
import sys

def test_http_requests():
    """Test HTTP requests to various sites"""
    test_sites = [
        "http://httpbin.org/get",  # Should not be blocked
        "http://example.com",      # Should be blocked (if in hosts file)
        "http://www.google.com",   # Should not be blocked
    ]
    
    print("Testing HTTP requests...")
    for url in test_sites:
        try:
            print(f"Making request to: {url}")
            response = requests.get(url, timeout=5)
            print(f"  Status: {response.status_code}")
        except Exception as e:
            print(f"  Error: {e}")
        time.sleep(2)

def test_https_requests():
    """Test HTTPS requests to various sites"""
    test_sites = [
        "https://httpbin.org/get",  # Should not be blocked
        "https://example.com",      # Should be blocked (if in hosts file)
        "https://www.google.com",   # Should not be blocked
    ]
    
    print("\nTesting HTTPS requests...")
    for url in test_sites:
        try:
            print(f"Making request to: {url}")
            response = requests.get(url, timeout=5)
            print(f"  Status: {response.status_code}")
        except Exception as e:
            print(f"  Error: {e}")
        time.sleep(2)

if __name__ == "__main__":
    print("Internet Filter Test Script")
    print("Make sure the Rust application is running with admin privileges!")
    print("Press Ctrl+C to stop\n")
    
    try:
        while True:
            test_http_requests()
            test_https_requests()
            print("\nWaiting 10 seconds before next test cycle...")
            time.sleep(10)
    except KeyboardInterrupt:
        print("\nTest stopped by user")
        sys.exit(0)
